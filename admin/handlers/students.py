from aiogram import Router, F
from aiogram.types import CallbackQuery, Message
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.filters import StateFilter

from admin.utils.common import (
    get_courses_list_kb, get_groups_by_course_kb, get_students_list_kb,
    get_confirmation_kb, get_tariff_selection_kb, add_student, remove_student, update_student,
    get_course_by_id, get_group_by_id, get_courses_selection_kb, get_student_groups_selection_kb,
    get_student_by_id, get_student_by_name, get_students_list, get_tariff_display_name
)
from admin.keyboards.main import get_home_kb, get_home_and_back_kb

router = Router()

class AdminStudentsStates(StatesGroup):
    # Состояния для добавления ученика
    enter_student_name = State()
    enter_student_telegram_id = State()
    select_student_courses = State()  # Множественный выбор курсов
    select_student_groups = State()   # Множественный выбор групп
    select_student_tariff = State()
    confirm_add_student = State()

    # Состояния для удаления ученика (используется только при редактировании)
    confirm_delete_student = State()

    # Состояния для просмотра и редактирования учеников
    students_list = State()
    edit_student = State()
    enter_new_student_name = State()
    edit_student_courses = State()
    edit_student_groups = State()
    edit_student_tariff = State()
    confirm_edit_student = State()

# === ДОБАВЛЕНИЕ УЧЕНИКА ===

@router.callback_query(F.data == "add_student")
async def start_add_student(callback: CallbackQuery, state: FSMContext):
    """Начать добавление ученика"""
    await callback.message.edit_text(
        text="Введите имя и фамилию ученика:",
        reply_markup=get_home_kb()
    )
    await state.set_state(AdminStudentsStates.enter_student_name)

@router.message(StateFilter(AdminStudentsStates.enter_student_name))
async def process_student_name(message: Message, state: FSMContext):
    """Обработать ввод имени ученика"""
    student_name = message.text.strip()

    # Проверяем, существует ли уже ученик с таким именем
    try:
        existing_student = await get_student_by_name(student_name)
        if existing_student:
            await message.answer(
                text=f"❌ Ученик с именем '{student_name}' уже существует!\n\n"
                     f"Введите другое имя ученика:",
                reply_markup=get_home_kb()
            )
            # Остаемся в том же состоянии для повторного ввода
            return
    except Exception as e:
        await message.answer(
            text=f"❌ Ошибка при проверке существования ученика!\n\n"
                 f"Причина: {str(e)}\n\n"
                 f"Попробуйте еще раз:",
            reply_markup=get_home_kb()
        )
        return

    await state.update_data(student_name=student_name)
    await state.set_state(AdminStudentsStates.enter_student_telegram_id)

    await message.answer(
        text="Введите Telegram ID ученика:",
        reply_markup=get_home_kb()
    )

@router.message(StateFilter(AdminStudentsStates.enter_student_telegram_id))
async def process_student_telegram_id(message: Message, state: FSMContext):
    """Обработать ввод Telegram ID ученика"""
    try:
        telegram_id = int(message.text.strip())

        # Проверяем существующего пользователя с учетом возможности самоназначения админа
        from admin.utils.common import check_existing_user_for_role_assignment
        check_result = await check_existing_user_for_role_assignment(
            telegram_id, 'student', message.from_user.id
        )

        if check_result['exists'] and not check_result['can_assign']:
            await message.answer(
                text=check_result['message'],
                reply_markup=get_home_kb()
            )
            return

        # Если пользователь существует и может быть назначен (админ добавляет себя)
        if check_result['exists'] and check_result['can_assign']:
            await message.answer(
                text=check_result['message'] + "\n\nПродолжаем назначение роли студента...",
                reply_markup=get_home_kb()
            )

        await state.update_data(student_telegram_id=telegram_id)
        await state.set_state(AdminStudentsStates.select_student_courses)

        await message.answer(
            text="Выберите курсы для ученика (можно выбрать несколько):\nВыбрано: 0",
            reply_markup=await get_courses_selection_kb([])
        )
    except ValueError:
        await message.answer(
            text="❌ Telegram ID должен быть числом. Попробуйте еще раз:",
            reply_markup=get_home_kb()
        )

# Обработчики множественного выбора курсов
@router.callback_query(AdminStudentsStates.select_student_courses, F.data.startswith("select_course_"))
async def select_course_for_student(callback: CallbackQuery, state: FSMContext):
    """Выбрать курс для студента"""
    course_id = int(callback.data.replace("select_course_", ""))
    data = await state.get_data()

    selected_course_ids = data.get("selected_course_ids", [])
    if course_id not in selected_course_ids:
        selected_course_ids.append(course_id)

    await state.update_data(selected_course_ids=selected_course_ids)

    await callback.message.edit_text(
        text=f"Выберите курсы для ученика (можно выбрать несколько):\nВыбрано: {len(selected_course_ids)}",
        reply_markup=await get_courses_selection_kb(selected_course_ids)
    )

@router.callback_query(AdminStudentsStates.select_student_courses, F.data.startswith("unselect_course_"))
async def unselect_course_for_student(callback: CallbackQuery, state: FSMContext):
    """Отменить выбор курса для студента"""
    course_id = int(callback.data.replace("unselect_course_", ""))
    data = await state.get_data()

    selected_course_ids = data.get("selected_course_ids", [])
    if course_id in selected_course_ids:
        selected_course_ids.remove(course_id)

    await state.update_data(selected_course_ids=selected_course_ids)

    await callback.message.edit_text(
        text=f"Выберите курсы для ученика (можно выбрать несколько):\nВыбрано: {len(selected_course_ids)}",
        reply_markup=await get_courses_selection_kb(selected_course_ids)
    )

@router.callback_query(AdminStudentsStates.select_student_courses, F.data == "finish_course_selection")
async def finish_course_selection_for_student(callback: CallbackQuery, state: FSMContext):
    """Завершить выбор курсов для студента"""
    data = await state.get_data()
    selected_course_ids = data.get("selected_course_ids", [])

    if not selected_course_ids:
        await callback.answer("❌ Выберите хотя бы один курс", show_alert=True)
        return

    # Получаем названия курсов по ID
    from database import CourseRepository
    course_names = []
    for course_id in selected_course_ids:
        course = await CourseRepository.get_by_id(course_id)
        if course:
            course_names.append(course.name)

    await state.update_data(student_course_names=course_names)
    await state.set_state(AdminStudentsStates.select_student_groups)

    await callback.message.edit_text(
        text=f"Курсы: {', '.join(course_names)}\n\nВыберите группы (можно выбрать несколько):\nВыбрано: 0",
        reply_markup=await get_student_groups_selection_kb([], selected_course_ids)
    )

# Обработчики множественного выбора групп для студентов
@router.callback_query(AdminStudentsStates.select_student_groups, F.data.startswith("select_student_group_"))
async def select_group_for_student(callback: CallbackQuery, state: FSMContext):
    """Выбрать группу для студента"""
    group_id = int(callback.data.replace("select_student_group_", ""))
    data = await state.get_data()

    selected_group_ids = data.get("selected_group_ids", [])
    selected_course_ids = data.get("selected_course_ids", [])
    course_names = data.get("student_course_names", [])

    if group_id not in selected_group_ids:
        selected_group_ids.append(group_id)

    await state.update_data(selected_group_ids=selected_group_ids)

    await callback.message.edit_text(
        text=f"Курсы: {', '.join(course_names)}\n\nВыберите группы (можно выбрать несколько):\nВыбрано: {len(selected_group_ids)}",
        reply_markup=await get_student_groups_selection_kb(selected_group_ids, selected_course_ids)
    )

@router.callback_query(AdminStudentsStates.select_student_groups, F.data.startswith("unselect_student_group_"))
async def unselect_group_for_student(callback: CallbackQuery, state: FSMContext):
    """Отменить выбор группы для студента"""
    group_id = int(callback.data.replace("unselect_student_group_", ""))
    data = await state.get_data()

    selected_group_ids = data.get("selected_group_ids", [])
    selected_course_ids = data.get("selected_course_ids", [])
    course_names = data.get("student_course_names", [])

    if group_id in selected_group_ids:
        selected_group_ids.remove(group_id)

    await state.update_data(selected_group_ids=selected_group_ids)

    await callback.message.edit_text(
        text=f"Курсы: {', '.join(course_names)}\n\nВыберите группы (можно выбрать несколько):\nВыбрано: {len(selected_group_ids)}",
        reply_markup=await get_student_groups_selection_kb(selected_group_ids, selected_course_ids)
    )

@router.callback_query(AdminStudentsStates.select_student_groups, F.data == "finish_student_group_selection")
async def finish_group_selection_for_student(callback: CallbackQuery, state: FSMContext):
    """Завершить выбор групп для студента"""
    data = await state.get_data()
    selected_group_ids = data.get("selected_group_ids", [])
    course_names = data.get("student_course_names", [])

    if not selected_group_ids:
        await callback.answer("❌ Выберите хотя бы одну группу", show_alert=True)
        return

    # Получаем названия групп по ID
    from database import GroupRepository
    group_names = []
    for group_id in selected_group_ids:
        group = await GroupRepository.get_by_id(group_id)
        if group:
            group_names.append(f"{group.name} ({group.subject.name})")

    await state.update_data(student_group_names=group_names)
    await state.set_state(AdminStudentsStates.select_student_tariff)

    await callback.message.edit_text(
        text=f"Курсы: {', '.join(course_names)}\nГруппы: {', '.join(group_names)}\n\nВыберите тариф:",
        reply_markup=get_tariff_selection_kb()
    )



@router.callback_query(AdminStudentsStates.select_student_tariff, F.data.startswith("tariff_"))
async def select_student_tariff(callback: CallbackQuery, state: FSMContext):
    """Выбрать тариф для ученика"""
    tariff_key = callback.data.replace("tariff_", "")
    tariff_name = get_tariff_display_name(tariff_key)

    data = await state.get_data()
    student_name = data.get("student_name", "")
    telegram_id = data.get("student_telegram_id", "")
    course_names = data.get("student_course_names", [])
    group_names = data.get("student_group_names", [])

    await state.update_data(student_tariff=tariff_name, student_tariff_name=tariff_name)
    await state.set_state(AdminStudentsStates.confirm_add_student)

    await callback.message.edit_text(
        text=f"📋 Подтверждение добавления:\n\n"
             f"Имя: {student_name}\n"
             f"Telegram ID: {telegram_id}\n"
             f"Курсы: {', '.join(course_names)}\n"
             f"Группы: {', '.join(group_names)}\n"
             f"Тариф: {tariff_name}",
        reply_markup=get_confirmation_kb("add", "student")
    )

@router.callback_query(StateFilter(AdminStudentsStates.confirm_add_student), F.data.startswith("confirm_add_student_"))
async def confirm_add_student(callback: CallbackQuery, state: FSMContext):
    """Подтвердить добавление ученика"""
    data = await state.get_data()

    student_name = data.get("student_name", "")
    telegram_id = data.get("student_telegram_id", "")
    tariff = data.get("student_tariff", "")
    selected_course_ids = data.get("selected_course_ids", [])
    selected_group_ids = data.get("selected_group_ids", [])

    # Добавляем ученика
    success = await add_student(student_name, int(telegram_id), tariff, selected_course_ids, selected_group_ids)

    if success:
        await callback.message.edit_text(
            text=f"✅ Ученик '{student_name}' успешно добавлен!",
            reply_markup=get_home_kb()
        )
    else:
        await callback.message.edit_text(
            text=f"❌ Ошибка при добавлении ученика '{student_name}'!\nВозможно, пользователь с таким Telegram ID уже существует.",
            reply_markup=get_home_kb()
        )

    await state.clear()

# === ПРОСМОТР И РЕДАКТИРОВАНИЕ УЧЕНИКОВ ===

@router.callback_query(F.data == "list_students")
async def show_students_list(callback: CallbackQuery, state: FSMContext):
    """Показать список учеников для редактирования"""
    try:
        # Получаем список учеников
        students = await get_students_list()

        if not students:
            await callback.message.edit_text(
                text="📋 Список учеников пуст!\n\n"
                     "Сначала добавьте учеников для управления ими.",
                reply_markup=get_home_kb()
            )
            return

        # Создаем клавиатуру со списком учеников для редактирования
        students_kb = await get_students_list_kb("edit_student")
        await callback.message.edit_text(
            text="👨‍🎓 Список учеников\n\n"
                 "Выберите ученика для редактирования:",
            reply_markup=students_kb
        )
        await state.set_state(AdminStudentsStates.students_list)

    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при загрузке списка учеников!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

@router.callback_query(StateFilter(AdminStudentsStates.students_list), F.data.startswith("edit_student_"))
async def start_edit_student(callback: CallbackQuery, state: FSMContext):
    """Начать редактирование ученика"""
    try:
        student_id = int(callback.data.replace("edit_student_", ""))
        student = await get_student_by_id(student_id)

        if not student:
            await callback.message.edit_text(
                text="❌ Ученик не найден!",
                reply_markup=get_home_kb()
            )
            return

        # Получаем названия курсов и групп ученика
        course_names = [course.name for course in student.courses]
        group_names = [f"{group.name} ({group.subject.name})" for group in student.groups]

        courses_text = ", ".join(course_names) if course_names else "Нет курсов"
        groups_text = ", ".join(group_names) if group_names else "Нет групп"

        await state.update_data(
            student_to_edit=student_id,
            current_student_name=student.user.name,
            current_student_tariff=student.tariff or "Не указан",
            current_course_ids=[course.id for course in student.courses],
            current_group_ids=[group.id for group in student.groups]
        )
        await state.set_state(AdminStudentsStates.edit_student)

        # Создаем клавиатуру для редактирования
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
        edit_kb = InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="✏️ Изменить имя", callback_data="change_student_name")],
            [InlineKeyboardButton(text="📚 Изменить курсы", callback_data="change_student_courses")],
            [InlineKeyboardButton(text="👥 Изменить группы", callback_data="change_student_groups")],
            [InlineKeyboardButton(text="💳 Изменить тариф", callback_data="change_student_tariff")],
            [InlineKeyboardButton(text="🗑 Удалить ученика", callback_data=f"delete_student_{student_id}")],
            [InlineKeyboardButton(text="⬅️ Назад к списку", callback_data="back_to_students_list")],
            *get_home_kb().inline_keyboard
        ])

        await callback.message.edit_text(
            text=f"👨‍🎓 Редактирование ученика\n\n"
                 f"📝 Имя: {student.user.name}\n"
                 f"🆔 Telegram ID: {student.user.telegram_id}\n"
                 f"💳 Тариф: {student.tariff or 'Не указан'}\n"
                 f"📚 Курсы: {courses_text}\n"
                 f"👥 Группы: {groups_text}\n"
                 f"🏆 Уровень: {student.level or 'Новичок'}\n"
                 f"⭐ Баллы: {student.points or 0}\n\n"
                 f"Выберите действие:",
            reply_markup=edit_kb
        )

    except ValueError as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при обработке ID ученика!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при загрузке данных ученика!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

@router.callback_query(StateFilter(AdminStudentsStates.edit_student), F.data == "change_student_name")
async def start_change_student_name(callback: CallbackQuery, state: FSMContext):
    """Начать изменение имени ученика"""
    data = await state.get_data()
    current_name = data.get("current_student_name", "")

    await callback.message.edit_text(
        text=f"✏️ Изменение имени ученика\n\n"
             f"Текущее имя: {current_name}\n\n"
             f"Введите новое имя ученика:",
        reply_markup=get_home_kb()
    )
    await state.set_state(AdminStudentsStates.enter_new_student_name)

@router.message(StateFilter(AdminStudentsStates.enter_new_student_name))
async def process_new_student_name(message: Message, state: FSMContext):
    """Обработать ввод нового имени ученика"""
    new_student_name = message.text.strip()
    data = await state.get_data()
    current_name = data.get("current_student_name", "")
    student_id = data.get("student_to_edit")

    # Проверяем, не совпадает ли новое имя с текущим
    if new_student_name == current_name:
        await message.answer(
            text=f"⚠️ Новое имя совпадает с текущим!\n\n"
                 f"Введите другое имя ученика:",
            reply_markup=get_home_kb()
        )
        return

    # Проверяем, существует ли уже ученик с таким именем
    try:
        existing_student = await get_student_by_name(new_student_name)
        if existing_student:
            await message.answer(
                text=f"❌ Ученик с именем '{new_student_name}' уже существует!\n\n"
                     f"Введите другое имя ученика:",
                reply_markup=get_home_kb()
            )
            return
    except Exception as e:
        await message.answer(
            text=f"❌ Ошибка при проверке существования ученика!\n\n"
                 f"Причина: {str(e)}\n\n"
                 f"Попробуйте еще раз:",
            reply_markup=get_home_kb()
        )
        return

    await state.update_data(new_student_name=new_student_name)
    await state.set_state(AdminStudentsStates.confirm_edit_student)

    await message.answer(
        text=f"📋 Подтверждение изменения ученика:\n\n"
             f"Текущее имя: {current_name}\n"
             f"Новое имя: {new_student_name}",
        reply_markup=get_confirmation_kb("edit", "student", str(student_id))
    )

@router.callback_query(StateFilter(AdminStudentsStates.edit_student), F.data == "change_student_courses")
async def start_change_student_courses(callback: CallbackQuery, state: FSMContext):
    """Начать изменение курсов ученика"""
    data = await state.get_data()
    current_course_ids = data.get("current_course_ids", [])
    student_name = data.get("current_student_name", "")

    await callback.message.edit_text(
        text=f"📚 Изменение курсов ученика '{student_name}'\n\n"
             f"Выберите курсы для ученика (можно выбрать несколько):\n"
             f"Выбрано: {len(current_course_ids)}",
        reply_markup=await get_courses_selection_kb(current_course_ids)
    )
    await state.update_data(selected_course_ids=current_course_ids)
    await state.set_state(AdminStudentsStates.edit_student_courses)

@router.callback_query(AdminStudentsStates.edit_student_courses, F.data.startswith("select_course_"))
async def select_course_for_student_edit(callback: CallbackQuery, state: FSMContext):
    """Выбрать курс для ученика при редактировании"""
    course_id = int(callback.data.replace("select_course_", ""))
    data = await state.get_data()
    student_name = data.get("current_student_name", "")

    selected_course_ids = data.get("selected_course_ids", [])
    if course_id not in selected_course_ids:
        selected_course_ids.append(course_id)

    await state.update_data(selected_course_ids=selected_course_ids)

    await callback.message.edit_text(
        text=f"📚 Изменение курсов ученика '{student_name}'\n\n"
             f"Выберите курсы для ученика (можно выбрать несколько):\n"
             f"Выбрано: {len(selected_course_ids)}",
        reply_markup=await get_courses_selection_kb(selected_course_ids)
    )

@router.callback_query(AdminStudentsStates.edit_student_courses, F.data.startswith("unselect_course_"))
async def unselect_course_for_student_edit(callback: CallbackQuery, state: FSMContext):
    """Отменить выбор курса для ученика при редактировании"""
    course_id = int(callback.data.replace("unselect_course_", ""))
    data = await state.get_data()
    student_name = data.get("current_student_name", "")

    selected_course_ids = data.get("selected_course_ids", [])
    if course_id in selected_course_ids:
        selected_course_ids.remove(course_id)

    await state.update_data(selected_course_ids=selected_course_ids)

    await callback.message.edit_text(
        text=f"📚 Изменение курсов ученика '{student_name}'\n\n"
             f"Выберите курсы для ученика (можно выбрать несколько):\n"
             f"Выбрано: {len(selected_course_ids)}",
        reply_markup=await get_courses_selection_kb(selected_course_ids)
    )

@router.callback_query(AdminStudentsStates.edit_student_courses, F.data == "finish_course_selection")
async def finish_course_selection_for_student_edit(callback: CallbackQuery, state: FSMContext):
    """Завершить выбор курсов при редактировании ученика"""
    from database import CourseRepository

    data = await state.get_data()
    student_name = data.get("current_student_name", "")
    student_id = data.get("student_to_edit")
    selected_course_ids = data.get("selected_course_ids", [])
    current_course_ids = data.get("current_course_ids", [])

    # Проверяем, изменился ли состав курсов
    if set(selected_course_ids) == set(current_course_ids):
        await callback.message.edit_text(
            text=f"⚠️ Состав курсов не изменился!\n\n"
                 f"Выберите другие курсы или вернитесь к редактированию ученика.",
            reply_markup=get_home_kb()
        )
        return

    # Получаем названия курсов по ID
    courses_names = []
    for course_id in selected_course_ids:
        course = await CourseRepository.get_by_id(course_id)
        if course:
            courses_names.append(course.name)

    # Получаем названия текущих курсов
    current_courses_names = []
    for course_id in current_course_ids:
        course = await CourseRepository.get_by_id(course_id)
        if course:
            current_courses_names.append(course.name)

    await state.update_data(new_course_ids=selected_course_ids)
    await state.set_state(AdminStudentsStates.confirm_edit_student)

    await callback.message.edit_text(
        text=f"📋 Подтверждение изменения курсов ученика:\n\n"
             f"Ученик: {student_name}\n\n"
             f"Текущие курсы: {', '.join(current_courses_names) if current_courses_names else 'Нет курсов'}\n\n"
             f"Новые курсы: {', '.join(courses_names) if courses_names else 'Нет курсов'}",
        reply_markup=get_confirmation_kb("edit", "student", str(student_id))
    )

@router.callback_query(StateFilter(AdminStudentsStates.edit_student), F.data == "change_student_groups")
async def start_change_student_groups(callback: CallbackQuery, state: FSMContext):
    """Начать изменение групп ученика"""
    data = await state.get_data()
    current_group_ids = data.get("current_group_ids", [])
    current_course_ids = data.get("current_course_ids", [])
    student_name = data.get("current_student_name", "")

    await callback.message.edit_text(
        text=f"👥 Изменение групп ученика '{student_name}'\n\n"
             f"Выберите группы для ученика (можно выбрать несколько):\n"
             f"Выбрано: {len(current_group_ids)}",
        reply_markup=await get_student_groups_selection_kb(current_group_ids, current_course_ids)
    )
    await state.update_data(selected_group_ids=current_group_ids)
    await state.set_state(AdminStudentsStates.edit_student_groups)

@router.callback_query(AdminStudentsStates.edit_student_groups, F.data.startswith("select_student_group_"))
async def select_group_for_student_edit(callback: CallbackQuery, state: FSMContext):
    """Выбрать группу для ученика при редактировании"""
    group_id = int(callback.data.replace("select_student_group_", ""))
    data = await state.get_data()
    student_name = data.get("current_student_name", "")

    selected_group_ids = data.get("selected_group_ids", [])
    print(f"🔍 DEBUG: Выбор группы {group_id} для студента {student_name}")
    print(f"🔍 DEBUG: selected_group_ids до изменения: {selected_group_ids}")

    if group_id not in selected_group_ids:
        selected_group_ids.append(group_id)
        print(f"🔍 DEBUG: Группа {group_id} добавлена")
    else:
        print(f"🔍 DEBUG: Группа {group_id} уже была выбрана")

    print(f"🔍 DEBUG: selected_group_ids после изменения: {selected_group_ids}")
    await state.update_data(selected_group_ids=selected_group_ids)

    await callback.message.edit_text(
        text=f"👥 Изменение групп ученика '{student_name}'\n\n"
             f"Выберите группы для ученика (можно выбрать несколько):\n"
             f"Выбрано: {len(selected_group_ids)}",
        reply_markup=await get_student_groups_selection_kb(selected_group_ids, data.get("current_course_ids", []))
    )

@router.callback_query(AdminStudentsStates.edit_student_groups, F.data.startswith("unselect_student_group_"))
async def unselect_group_for_student_edit(callback: CallbackQuery, state: FSMContext):
    """Отменить выбор группы для ученика при редактировании"""
    group_id = int(callback.data.replace("unselect_student_group_", ""))
    data = await state.get_data()
    student_name = data.get("current_student_name", "")

    selected_group_ids = data.get("selected_group_ids", [])
    if group_id in selected_group_ids:
        selected_group_ids.remove(group_id)

    await state.update_data(selected_group_ids=selected_group_ids)

    await callback.message.edit_text(
        text=f"👥 Изменение групп ученика '{student_name}'\n\n"
             f"Выберите группы для ученика (можно выбрать несколько):\n"
             f"Выбрано: {len(selected_group_ids)}",
        reply_markup=await get_student_groups_selection_kb(selected_group_ids, data.get("current_course_ids", []))
    )

@router.callback_query(AdminStudentsStates.edit_student_groups, F.data == "finish_student_group_selection")
async def finish_group_selection_for_student_edit(callback: CallbackQuery, state: FSMContext):
    """Завершить выбор групп при редактировании ученика"""
    from database import GroupRepository

    data = await state.get_data()
    student_name = data.get("current_student_name", "")
    student_id = data.get("student_to_edit")
    selected_group_ids = data.get("selected_group_ids", [])
    current_group_ids = data.get("current_group_ids", [])

    # Отладочная информация
    print(f"🔍 DEBUG: Проверка изменений групп для студента {student_name}")
    print(f"🔍 DEBUG: current_group_ids = {current_group_ids}")
    print(f"🔍 DEBUG: selected_group_ids = {selected_group_ids}")
    print(f"🔍 DEBUG: set(current_group_ids) = {set(current_group_ids)}")
    print(f"🔍 DEBUG: set(selected_group_ids) = {set(selected_group_ids)}")
    print(f"🔍 DEBUG: Равны ли множества? {set(selected_group_ids) == set(current_group_ids)}")

    # Проверяем, изменился ли состав групп
    if set(selected_group_ids) == set(current_group_ids):
        await callback.message.edit_text(
            text=f"⚠️ Состав групп не изменился!\n\n"
                 f"Выберите другие группы или вернитесь к редактированию ученика.",
            reply_markup=get_home_kb()
        )
        return

    # Получаем названия групп по ID
    groups_names = []
    for group_id in selected_group_ids:
        group = await GroupRepository.get_by_id(group_id)
        if group:
            groups_names.append(f"{group.name} ({group.subject.name})")

    # Получаем названия текущих групп
    current_groups_names = []
    for group_id in current_group_ids:
        group = await GroupRepository.get_by_id(group_id)
        if group:
            current_groups_names.append(f"{group.name} ({group.subject.name})")

    await state.update_data(new_group_ids=selected_group_ids)
    await state.set_state(AdminStudentsStates.confirm_edit_student)

    await callback.message.edit_text(
        text=f"📋 Подтверждение изменения групп ученика:\n\n"
             f"Ученик: {student_name}\n\n"
             f"Текущие группы: {', '.join(current_groups_names) if current_groups_names else 'Нет групп'}\n\n"
             f"Новые группы: {', '.join(groups_names) if groups_names else 'Нет групп'}",
        reply_markup=get_confirmation_kb("edit", "student", str(student_id))
    )

@router.callback_query(StateFilter(AdminStudentsStates.edit_student), F.data == "change_student_tariff")
async def start_change_student_tariff(callback: CallbackQuery, state: FSMContext):
    """Начать изменение тарифа ученика"""
    data = await state.get_data()
    current_tariff = data.get("current_student_tariff", "Не указан")
    student_name = data.get("current_student_name", "")

    await callback.message.edit_text(
        text=f"💳 Изменение тарифа ученика '{student_name}'\n\n"
             f"Текущий тариф: {current_tariff}\n\n"
             f"Выберите новый тариф:",
        reply_markup=get_tariff_selection_kb()
    )
    await state.set_state(AdminStudentsStates.edit_student_tariff)

@router.callback_query(StateFilter(AdminStudentsStates.edit_student_tariff), F.data.startswith("tariff_"))
async def select_new_student_tariff(callback: CallbackQuery, state: FSMContext):
    """Выбрать новый тариф для ученика"""
    tariff_key = callback.data.replace("tariff_", "")
    new_tariff = get_tariff_display_name(tariff_key)
    data = await state.get_data()
    current_tariff = data.get("current_student_tariff", "Не указан")
    student_name = data.get("current_student_name", "")
    student_id = data.get("student_to_edit")

    # Проверяем, не совпадает ли новый тариф с текущим
    if new_tariff == current_tariff:
        await callback.message.edit_text(
            text=f"⚠️ Новый тариф совпадает с текущим!\n\n"
                 f"Выберите другой тариф:",
            reply_markup=get_tariff_selection_kb()
        )
        return

    await state.update_data(new_student_tariff=new_tariff)
    await state.set_state(AdminStudentsStates.confirm_edit_student)

    await callback.message.edit_text(
        text=f"📋 Подтверждение изменения тарифа ученика:\n\n"
             f"Ученик: {student_name}\n"
             f"Текущий тариф: {current_tariff}\n"
             f"Новый тариф: {new_tariff}",
        reply_markup=get_confirmation_kb("edit", "student", str(student_id))
    )

@router.callback_query(StateFilter(AdminStudentsStates.confirm_edit_student), F.data.startswith("confirm_edit_student_"))
async def confirm_edit_student(callback: CallbackQuery, state: FSMContext):
    """Подтвердить редактирование ученика"""
    data = await state.get_data()
    student_id = data.get("student_to_edit")
    new_name = data.get("new_student_name")
    new_tariff = data.get("new_student_tariff")
    new_course_ids = data.get("new_course_ids")
    new_group_ids = data.get("new_group_ids")
    current_name = data.get("current_student_name", "")
    current_tariff = data.get("current_student_tariff", "")

    try:
        # Определяем, что именно обновляем
        changes = []
        if new_name:
            changes.append(f"Имя: {current_name} → {new_name}")
        if new_tariff:
            changes.append(f"Тариф: {current_tariff} → {new_tariff}")
        if new_course_ids is not None:
            changes.append("Курсы обновлены")
        if new_group_ids is not None:
            changes.append("Группы обновлены")

        if not changes:
            await callback.message.edit_text(
                text="⚠️ Нет данных для обновления!",
                reply_markup=get_home_kb()
            )
            await state.clear()
            return

        # Обновляем ученика в базе данных
        success = await update_student(
            student_id,
            name=new_name,
            tariff=new_tariff,
            course_ids=new_course_ids,
            group_ids=new_group_ids
        )

        if success:
            await callback.message.edit_text(
                text=f"✅ Ученик успешно обновлен!\n\n" + "\n".join(changes),
                reply_markup=get_home_kb()
            )
        else:
            await callback.message.edit_text(
                text=f"❌ Ошибка при обновлении ученика!\n"
                     f"Возможно, ученик с таким именем уже существует.",
                reply_markup=get_home_kb()
            )
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при обновлении ученика!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

    await state.clear()

@router.callback_query(StateFilter(AdminStudentsStates.edit_student), F.data == "back_to_students_list")
async def back_to_students_list(callback: CallbackQuery, state: FSMContext):
    """Вернуться к списку учеников"""
    # Вызываем функцию показа списка учеников
    await show_students_list(callback, state)

# === УДАЛЕНИЕ УЧЕНИКА (только через редактирование) ===

@router.callback_query(StateFilter(AdminStudentsStates.edit_student), F.data.startswith("delete_student_"))
async def select_student_to_delete(callback: CallbackQuery, state: FSMContext):
    """Выбрать ученика для удаления из режима редактирования"""
    try:
        student_id = int(callback.data.replace("delete_student_", ""))
        student = await get_student_by_id(student_id)

        if not student:
            await callback.message.edit_text(
                text="❌ Ученик не найден!",
                reply_markup=get_home_kb()
            )
            return

        await state.update_data(
            student_to_delete=student_id,
            student_name=student.user.name
        )
        await state.set_state(AdminStudentsStates.confirm_delete_student)

        await callback.message.edit_text(
            text=f"🗑 Подтверждение удаления ученика:\n\n"
                 f"Имя: {student.user.name}\n"
                 f"Telegram ID: {student.user.telegram_id}\n"
                 f"Тариф: {student.tariff or 'Не указан'}\n\n"
                 f"⚠️ Это действие нельзя отменить!",
            reply_markup=get_confirmation_kb("delete", "student", str(student_id))
        )

    except ValueError as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при обработке ID ученика!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при загрузке данных ученика!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

@router.callback_query(StateFilter(AdminStudentsStates.confirm_delete_student), F.data.startswith("confirm_delete_student_"))
async def confirm_delete_student(callback: CallbackQuery, state: FSMContext):
    """Подтвердить удаление ученика"""
    data = await state.get_data()
    student_id = data.get("student_to_delete")
    student_name = data.get("student_name", "")

    success = await remove_student(student_id)

    if success:
        await callback.message.edit_text(
            text=f"✅ Ученик '{student_name}' успешно удален!",
            reply_markup=get_home_kb()
        )
    else:
        await callback.message.edit_text(
            text="❌ Ученик не найден!",
            reply_markup=get_home_kb()
        )

    await state.clear()

# === ОБРАБОТЧИКИ ОТМЕНЫ РЕДАКТИРОВАНИЯ ===

@router.callback_query(StateFilter(AdminStudentsStates.confirm_edit_student), F.data.startswith("cancel_edit_student"))
async def cancel_edit_student(callback: CallbackQuery, state: FSMContext):
    """Отменить редактирование ученика"""
    await callback.message.edit_text(
        text="❌ Редактирование ученика отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()

# === ОТМЕНА ДЕЙСТВИЙ ===

@router.callback_query(StateFilter(AdminStudentsStates.confirm_add_student), F.data.startswith("cancel_add_student"))
async def cancel_add_student(callback: CallbackQuery, state: FSMContext):
    """Отменить добавление ученика"""
    await callback.message.edit_text(
        text="❌ Добавление ученика отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()

@router.callback_query(StateFilter(AdminStudentsStates.confirm_delete_student), F.data.startswith("cancel_delete_student"))
async def cancel_delete_student(callback: CallbackQuery, state: FSMContext):
    """Отменить удаление ученика"""
    await callback.message.edit_text(
        text="❌ Удаление ученика отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()
